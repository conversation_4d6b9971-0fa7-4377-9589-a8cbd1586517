{"name": "out", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:seed": "prisma db seed"}, "prisma": {"seed": "node ./prisma/seed.js"}, "dependencies": {"@auth/prisma-adapter": "^2.9.0", "@floating-ui/react": "^0.27.3", "@headlessui-float/react": "^0.15.0", "@headlessui/react": "^2.2.0", "@maptiler/geocoding-control": "^2.1.6", "@next/third-parties": "^15.3.1", "@prisma/client": "^5.22.0", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-select": "^2.1.4", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.8", "aos": "^2.3.4", "argon2": "^0.41.1", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "leaflet": "^1.9.4", "lucide-react": "^0.471.0", "maplibre-gl": "^5.4.0", "next": "^15.3.1", "next-auth": "^4.24.11", "nextjs-toploader": "^3.7.15", "preline": "^3.0.1", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-icons": "^5.4.0", "react-text-typist": "^1.1.8", "recharts": "^3.0.2", "swiper": "^11.2.6", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "winston": "^3.17.0", "zod": "^3.22.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/aos": "^3.0.7", "@types/leaflet": "^1.9.17", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.4", "postcss": "^8", "prisma": "^5.11.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}