generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Terminal {
  id        String   @id @default(cuid())
  nombre    String   @unique
  activo    Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  precios   Precio[]

  @@index([nombre])
}

model Precio {
  id         String   @id @default(cuid())
  producto   String
  terminalId String
  fecha      DateTime
  valor      Float
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  terminal   Terminal @relation(fields: [terminalId], references: [id])

  @@unique([producto, terminalId, fecha])
  @@index([producto, terminalId, fecha])
  @@index([fecha])
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  phone         String?
  emailVerified DateTime?
  password      String
  image         String?
  role          String    @default("USER") // Valores posibles: "USER", "ADMIN", "SUPER_ADMIN", "WORKER"
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  accounts      Account[]
  sessions      Session[]
  kpisSemanales KpiSemanal[]
  kpisCompras   KpiCompras[]
  appConfigs    AppConfig[]
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Lead {
  id              String    @id @default(cuid())
  email           String    @unique
  ipAddress       String?
  userAgent       String?
  source          String?
  convertedToUser Boolean   @default(false)
  lastAttempt     DateTime?
  attemptCount    Int       @default(0)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@index([email])
  @@index([convertedToUser])
}

model TarifaKm {
  id        String   @id @default(cuid())
  distancia Int      @unique
  tarifa    Float
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([distancia])
}

model KpiSemanal {
  id                          String   @id @default(cuid())
  userId                      String   // Usuario que capturó los datos
  year                        Int      // Año (ej: 2025)
  weekNumber                  Int      // Número de semana (ej: 27)
  weekStartDate               DateTime // Fecha de inicio de la semana (ej: 2025-06-30)
  weekEndDate                 DateTime // Fecha de fin de la semana (ej: 2025-07-06)

  // Los 8 indicadores de ventas semanales
  volumenTotalLitros          Float    // VOLUMEN TOTAL DE VENTA POR MES (LITROS)
  crecimientoMensual          Float    // CRECIMIENTO MENSUAL DE VENTAS (%)
  margenBrutoPorLitro         Float    // MARGEN BRUTO POR LITRO VENDIDO
  tasaRetencionClientes       Float    // TASA DE RETENCIÓN DE CLIENTES
  cumplimientoObjetivo        Float    // PORCENTAJE DE CUMPLIMIENTO DEL OBJETIVO DE VENTAS MENSUAL
  desviacionVentas            Float    // DESVIACIÓN ENTRE VENTAS PROYECTADAS Y REALES
  cicloPromedioCierre         Int      // CICLO PROMEDIO DE CIERRE DE VENTAS (DÍAS)
  clientesActivosMensuales    Int      // NÚMERO DE CLIENTES ACTIVOS MENSUALES

  createdAt                   DateTime @default(now())
  updatedAt                   DateTime @updatedAt
  user                        User     @relation(fields: [userId], references: [id])

  @@unique([year, weekNumber]) // Una sola entrada por semana del año
  @@index([year, weekNumber])
  @@index([userId])
  @@index([weekStartDate])
}

model KpiCompras {
  id                          String   @id @default(cuid())
  userId                      String   // Usuario que capturó los datos
  year                        Int      // Año (ej: 2025)
  weekNumber                  Int      // Número de semana (ej: 27)
  weekStartDate               DateTime // Fecha de inicio de la semana (ej: 2025-06-30)
  weekEndDate                 DateTime // Fecha de fin de la semana (ej: 2025-07-06)

  // Los 4 indicadores principales de compras semanales
  numeroProveedoresActivos    Int      // NÚMERO DE PROVEEDORES ACTIVOS
  porcentajeReporteGanancia   Float    // PORCENTAJE DE REPORTE DE GANANCIA OPERATIVA
  preciosPromedioCompra       Float    // PRECIOS PROMEDIOS DE COMPRA
  diferencialPrecioPemex      Float    // DIFERENCIAL ENTRE PRECIO DE COMPRA REAL Y PRECIO DE TERMINAL PEMEX

  // Distribución porcentual por proveedor (% COMPRA POR PROVEEDOR) - JSON flexible
  distribucionProveedores     String   // JSON string con array de {nombre: string, porcentaje: number}

  createdAt                   DateTime @default(now())
  updatedAt                   DateTime @updatedAt
  user                        User     @relation(fields: [userId], references: [id])

  @@unique([year, weekNumber]) // Una sola entrada por semana del año
  @@index([year, weekNumber])
  @@index([userId])
  @@index([weekStartDate])
}

model AppConfig {
  id                          String   @id @default(cuid())
  key                         String   @unique // Clave de configuración (ej: "tacometro_max_proveedores")
  value                       String   // Valor de configuración (JSON string para valores complejos)
  description                 String?  // Descripción opcional de la configuración
  userId                      String   // Usuario que modificó la configuración
  createdAt                   DateTime @default(now())
  updatedAt                   DateTime @updatedAt
  user                        User     @relation(fields: [userId], references: [id])

  @@index([key])
  @@index([userId])
}
