/**
 * Utilidades para manejo de semanas del año
 * Las semanas van de jueves a miércoles para alinearse con el ciclo de negocio
 */

export interface WeekInfo {
  year: number;
  weekNumber: number;
  startDate: Date;
  endDate: Date;
  label: string; // ej: "Semana 27/2025 (30/06/2025 - 06/07/2025)"
}

/**
 * Obtiene el número de semana para una fecha dada (jueves a miércoles)
 * @param date Fecha
 * @returns Número de semana (1-53)
 */
export function getISOWeekNumber(date: Date): number {
  const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
  const dayNum = d.getUTCDay(); // 0=domingo, 1=lunes, ..., 6=sábado

  // Encontrar el jueves de la semana que contiene esta fecha
  let thursdayOfWeek = new Date(d);

  if (dayNum >= 0 && dayNum <= 3) {
    // Si es domingo, lunes, martes o miércoles, ir al jueves anterior
    const daysToSubtract = dayNum === 0 ? 3 : dayNum + 3;
    thursdayOfWeek.setUTCDate(d.getUTCDate() - daysToSubtract);
  } else {
    // Si es jueves, viernes o sábado, ir al jueves de esa semana
    const daysToSubtract = dayNum - 4;
    thursdayOfWeek.setUTCDate(d.getUTCDate() - daysToSubtract);
  }

  // Determinar el año de la semana
  const weekYear = thursdayOfWeek.getUTCFullYear();

  // Encontrar el primer jueves del año de la semana
  const jan1 = new Date(Date.UTC(weekYear, 0, 1));
  const firstThursday = new Date(jan1);
  const firstDayOfWeek = jan1.getUTCDay();
  const daysToFirstThursday = firstDayOfWeek <= 4 ? 4 - firstDayOfWeek : 11 - firstDayOfWeek;
  firstThursday.setUTCDate(jan1.getUTCDate() + daysToFirstThursday);

  // Calcular la diferencia en días y convertir a semanas
  const diffTime = thursdayOfWeek.getTime() - firstThursday.getTime();
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  return Math.floor(diffDays / 7) + 1;
}

/**
 * Obtiene el año para una fecha dada (basado en semanas jueves-miércoles)
 * @param date Fecha
 * @returns Año
 */
export function getISOYear(date: Date): number {
  const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
  const dayNum = d.getUTCDay();

  // Encontrar el jueves de la semana que contiene esta fecha
  let thursdayOfWeek = new Date(d);

  if (dayNum >= 0 && dayNum <= 3) {
    // Si es domingo, lunes, martes o miércoles, ir al jueves anterior
    const daysToSubtract = dayNum === 0 ? 3 : dayNum + 3;
    thursdayOfWeek.setUTCDate(d.getUTCDate() - daysToSubtract);
  } else {
    // Si es jueves, viernes o sábado, ir al jueves de esa semana
    const daysToSubtract = dayNum - 4;
    thursdayOfWeek.setUTCDate(d.getUTCDate() - daysToSubtract);
  }

  // El año de la semana es el año del jueves
  return thursdayOfWeek.getUTCFullYear();
}

/**
 * Obtiene las fechas de inicio y fin de una semana específica (jueves a miércoles)
 * @param year Año
 * @param weekNumber Número de semana
 * @returns Objeto con fechas de inicio y fin
 */
export function getWeekDates(year: number, weekNumber: number): { startDate: Date; endDate: Date } {
  // Validar parámetros de entrada
  if (!year || year < 1900 || year > 2100) {
    throw new Error(`Año inválido: ${year}`);
  }

  if (!weekNumber || weekNumber < 1 || weekNumber > 53) {
    throw new Error(`Número de semana inválido: ${weekNumber}`);
  }

  // Primer día del año
  const jan1 = new Date(year, 0, 1);

  // Encontrar el primer jueves del año
  const firstThursday = new Date(jan1);
  const dayOfWeek = jan1.getDay(); // 0=domingo, 1=lunes, ..., 6=sábado

  // Calcular días hasta el primer jueves
  let daysToFirstThursday;
  if (dayOfWeek <= 4) {
    // Si el 1 de enero es domingo a jueves
    daysToFirstThursday = 4 - dayOfWeek;
  } else {
    // Si el 1 de enero es viernes o sábado
    daysToFirstThursday = 11 - dayOfWeek;
  }

  firstThursday.setDate(jan1.getDate() + daysToFirstThursday);

  // Calcular la fecha de inicio de la semana solicitada (jueves)
  const startDate = new Date(firstThursday);
  startDate.setDate(firstThursday.getDate() + (weekNumber - 1) * 7);

  // La fecha de fin es 6 días después del inicio (miércoles)
  const endDate = new Date(startDate);
  endDate.setDate(startDate.getDate() + 6);

  // Validar que las fechas generadas sean válidas
  if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
    throw new Error(`Fechas inválidas generadas para año ${year}, semana ${weekNumber}`);
  }

  return { startDate, endDate };
}

/**
 * Obtiene información completa de la semana actual
 * @returns Información de la semana actual
 */
export function getCurrentWeekInfo(): WeekInfo {
  const now = new Date();
  const year = getISOYear(now);
  const weekNumber = getISOWeekNumber(now);
  const { startDate, endDate } = getWeekDates(year, weekNumber);
  
  return {
    year,
    weekNumber,
    startDate,
    endDate,
    label: formatWeekLabel(year, weekNumber, startDate, endDate)
  };
}

/**
 * Obtiene información de una semana específica
 * @param year Año
 * @param weekNumber Número de semana
 * @returns Información de la semana
 */
export function getWeekInfo(year: number, weekNumber: number): WeekInfo {
  try {
    const { startDate, endDate } = getWeekDates(year, weekNumber);

    return {
      year,
      weekNumber,
      startDate,
      endDate,
      label: formatWeekLabel(year, weekNumber, startDate, endDate)
    };
  } catch (error) {
    // Si hay un error, devolver información de la semana 1 como fallback
    console.warn(`Error al obtener semana ${weekNumber}/${year}, usando semana 1 como fallback:`, error);
    const { startDate, endDate } = getWeekDates(year, 1);

    return {
      year,
      weekNumber: 1,
      startDate,
      endDate,
      label: formatWeekLabel(year, 1, startDate, endDate)
    };
  }
}

/**
 * Formatea el label de una semana
 * @param year Año
 * @param weekNumber Número de semana
 * @param startDate Fecha de inicio
 * @param endDate Fecha de fin
 * @returns Label formateado
 */
export function formatWeekLabel(year: number, weekNumber: number, startDate: Date, endDate: Date): string {
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };
  
  return `Semana ${weekNumber}/${year} (${formatDate(startDate)} - ${formatDate(endDate)})`;
}

/**
 * Obtiene las últimas N semanas incluyendo la actual
 * @param count Número de semanas a obtener
 * @returns Array de información de semanas
 */
export function getLastWeeks(count: number): WeekInfo[] {
  const weeks: WeekInfo[] = [];
  const currentWeek = getCurrentWeekInfo();
  
  for (let i = count - 1; i >= 0; i--) {
    const targetDate = new Date();
    targetDate.setDate(targetDate.getDate() - (i * 7));
    
    const year = getISOYear(targetDate);
    const weekNumber = getISOWeekNumber(targetDate);
    const weekInfo = getWeekInfo(year, weekNumber);
    
    weeks.push(weekInfo);
  }
  
  return weeks;
}

/**
 * Valida si una fecha está dentro de una semana específica
 * @param date Fecha a validar
 * @param year Año de la semana
 * @param weekNumber Número de semana
 * @returns true si la fecha está en la semana
 */
export function isDateInWeek(date: Date, year: number, weekNumber: number): boolean {
  const { startDate, endDate } = getWeekDates(year, weekNumber);
  return date >= startDate && date <= endDate;
}
